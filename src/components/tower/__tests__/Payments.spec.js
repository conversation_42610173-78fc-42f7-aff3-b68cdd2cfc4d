import { describe, it, expect, vi } from 'vitest';
import Payments from '../Payments.vue';

describe('Payments.vue - Customer Change Bug Fix', () => {
  it('should update both viewConfig and callsConfig when customer changes', () => {
    // Test the watcher function directly without mounting the component
    const mockLoadData = vi.fn();

    // Create a mock component instance with the necessary data
    const mockComponent = {
      viewConfig: {
        dataAdditional: {
          Customer: 123
        }
      },
      callsConfig: {
        dataAdditional: {
          Customer: 123
        }
      },
      $store: {
        state: {
          payment: {
            selectedCustomerKey: 456
          }
        }
      },
      loadData: mockLoadData
    };

    // Get the watcher function from the component definition
    const watcher = Payments.watch['$store.state.payment.selectedCustomerKey'];

    // Call the watcher with the mock component as context
    watcher.call(mockComponent);

    // Verify both configs were updated with the new customer
    expect(mockComponent.viewConfig.dataAdditional.Customer).toBe(456);
    expect(mockComponent.callsConfig.dataAdditional.Customer).toBe(456);

    // Verify loadData was called to refresh the grids
    expect(mockLoadData).toHaveBeenCalled();
  });

  it('should handle null customer selection', () => {
    const mockLoadData = vi.fn();

    const mockComponent = {
      viewConfig: {
        dataAdditional: {
          Customer: 123
        }
      },
      callsConfig: {
        dataAdditional: {
          Customer: 123
        }
      },
      $store: {
        state: {
          payment: {
            selectedCustomerKey: null
          }
        }
      },
      loadData: mockLoadData
    };

    const watcher = Payments.watch['$store.state.payment.selectedCustomerKey'];
    watcher.call(mockComponent);

    expect(mockComponent.viewConfig.dataAdditional.Customer).toBe(null);
    expect(mockComponent.callsConfig.dataAdditional.Customer).toBe(null);
    expect(mockLoadData).toHaveBeenCalled();
  });

  it('should handle empty string customer selection', () => {
    const mockLoadData = vi.fn();

    const mockComponent = {
      viewConfig: {
        dataAdditional: {
          Customer: 123
        }
      },
      callsConfig: {
        dataAdditional: {
          Customer: 123
        }
      },
      $store: {
        state: {
          payment: {
            selectedCustomerKey: ''
          }
        }
      },
      loadData: mockLoadData
    };

    const watcher = Payments.watch['$store.state.payment.selectedCustomerKey'];
    watcher.call(mockComponent);

    expect(mockComponent.viewConfig.dataAdditional.Customer).toBe('');
    expect(mockComponent.callsConfig.dataAdditional.Customer).toBe('');
    expect(mockLoadData).toHaveBeenCalled();
  });

  it('should verify the watcher exists and is a function', () => {
    // Verify the watcher exists in the component definition
    expect(Payments.watch).toBeDefined();
    expect(Payments.watch['$store.state.payment.selectedCustomerKey']).toBeDefined();
    expect(typeof Payments.watch['$store.state.payment.selectedCustomerKey']).toBe('function');
  });

  it('should verify component data structure includes both configs', () => {
    // Test the component's data function with a mock context
    const mockContext = {
      $store: {
        state: {
          payment: {
            showDeletedPayments: false
          }
        }
      }
    };

    const componentData = Payments.data.call(mockContext);

    expect(componentData.viewConfig).toBeDefined();
    expect(componentData.callsConfig).toBeDefined();
    expect(componentData.viewConfig.dataAdditional).toBeDefined();
    expect(componentData.callsConfig.dataAdditional).toBeDefined();

    // Verify they are separate objects
    expect(componentData.viewConfig).not.toBe(componentData.callsConfig);

    // Verify they have different UUIDs
    expect(componentData.viewConfig.uuid).toBe('payments-view');
    expect(componentData.callsConfig.uuid).toBe('calls-grid');
  });

  it('should handle null/undefined data gracefully in computed properties', () => {
    // Test that computed properties handle null/undefined data without throwing errors
    const mockComponent = {
      callsData: null,
      paymentsData: undefined,
      selectedPayment: null,
      $store: {
        state: {
          payment: {
            selectedCustomerKey: 123
          }
        }
      },
      loadData: vi.fn()
    };

    // Test pendingApplications computed property
    const pendingApplications = Payments.computed.pendingApplications.call(mockComponent);
    expect(pendingApplications).toEqual([]);

    // Test sumOfCallsBalance computed property
    const sumOfCallsBalance = Payments.computed.sumOfCallsBalance.call(mockComponent);
    expect(sumOfCallsBalance).toBe(0);

    // Test sumOfPaymentsBalance computed property
    const sumOfPaymentsBalance = Payments.computed.sumOfPaymentsBalance.call(mockComponent);
    expect(sumOfPaymentsBalance).toBe(0);

    // Test with empty arrays
    mockComponent.callsData = [];
    mockComponent.paymentsData = [];

    expect(Payments.computed.pendingApplications.call(mockComponent)).toEqual([]);
    expect(Payments.computed.sumOfCallsBalance.call(mockComponent)).toBe(0);
    expect(Payments.computed.sumOfPaymentsBalance.call(mockComponent)).toBe(0);
  });

  it('should handle methods gracefully when data is null/undefined', () => {
    const mockComponent = {
      callsData: null,
      paymentsData: null,
      selectedPayment: null
    };

    // Test resetApplication method
    expect(() => {
      Payments.methods.resetApplication.call(mockComponent);
    }).not.toThrow();

    // Test afterLoadData method
    expect(() => {
      Payments.methods.afterLoadData.call(mockComponent);
    }).not.toThrow();

    // Test changePendingPayment method
    expect(() => {
      Payments.methods.changePendingPayment.call(mockComponent, { lKey: 123 });
    }).not.toThrow();

    // Test distributePaymentBalance method
    expect(() => {
      Payments.methods.distributePaymentBalance.call(mockComponent);
    }).not.toThrow();
  });
});
