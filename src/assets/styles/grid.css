.data-grid {
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: var(--titlebar-height) 1fr;
  grid-template-areas:
    "title-bar"
    "viewport";
  width: 100%;
  height: 100%;

  > .title-bar {
    grid-area: title-bar;
    height: var(--titlebar-height);
  }

  > .viewport {
    grid-area: viewport;

    display: grid;

    padding-bottom: 6rem;
    min-width: 100%;
    overflow: auto;
    color: var(--body-fg);
    background: var(--body-bg);
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
    user-select: none;

    ._liner {
      display: flex;
      width: max-content; /* Allow container to expand to fit all columns */
      min-width: 100%; /* Ensure it's at least as wide as the viewport */
    }

    ._floating-tools {
      position: absolute;
      right: 1rem;
      bottom: 1rem;

      display: flex;
      justify-content: flex-end;
      gap: 0.5rem;

      pointer-events: none;

      > .pill {
        display: flex;
        gap: 0.25rem;

        width: min-content;
        background-color: var(--header-background);
        backdrop-filter: var(--blur);
        padding: 0.5rem;
        border-radius: 10rem;
        box-shadow: var(--box-shadow);

        > .divider {
          height: 100%;
          width: 0;
          border-right: 1px solid var(--input-border);
        }

        > .button {
          border-radius: 10rem;
          pointer-events: initial;
        }
      }
    }

    .-column {
      position: relative;

      /* Use width property directly, with var(--width) as fallback */
      width: var(--width);
      min-height: 100%;
      height: max-content;
      box-sizing: border-box;
      flex-shrink: 0; /* Prevent columns from shrinking */

      &.-action {
        .-button {
          border: none;
          color: var(--body-fg);
          background: none;
        }

        .-error {
          position: relative;
          color: var(--danger);

          .-icon {
            position: relative;
          }

          .-detail {
            position: absolute;
            left: 0;
            top: 0;

            visibility: hidden;
            text-align: left;
            padding: 1em 1em 1em 3em;
            color: var(--body-fg);
            background: var(--body-bg);
            border: 1px solid var(--body-border);
            border-radius: var(--border-radius-100);
            box-shadow: var(--box-shadow-100);
            z-index: 10;
          }

          &:hover {
            .-icon {
              z-index: 11;
            }

            .-detail {
              visibility: visible;
            }
          }
        }
      }

      &.-flexer {
        width: 100%;
      }
    }

    .-row {
      --row-color: var(--pure-black);
      --row-background: var(--white);
      --row-border-color: color-mix(in oklch, var(--concrete), transparent 40%);

      --selected-color: color-mix(in oklch, var(--selected-background), var(--pure-black) 70%);
      --selected-background: color-mix(in oklch, var(--row-background), var(--pure-blue) 20%);

      --flagged-color: color-mix(in oklch, var(--flagged-background), var(--pure-black) 70%);
      --flagged-background: color-mix(in oklch, var(--white), var(--pure-red) 20%);

      --flagged-selected-color: color-mix(in oklch, var(--flagged-selected-background), var(--pure-black) 70%);
      --flagged-selected-background: color-mix(in oklch, var(--flagged-background), var(--pure-blue) 20%);

      --header-background: color-mix(in oklch, var(--white), transparent 20%);

      display: flex;
      justify-content: left;
      align-items: center;

      height: 2.5rem;
      padding: 0 0.5em;
      color: var(--row-color);
      background-color: var(--row-background);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      border-bottom: 1px solid var(--row-border-color);

      &.-allow-overflow {
        overflow: visible;
      }

      &.-ghost {
        color: color-mix(in oklch, var(--body-fg), transparent 50%);
      }

      &.-highlighted {
        color: var(--selected-color);
        background-color: var(--selected-background);
        /* Keep the border visible for highlighted rows */
        border-bottom-color: var(--row-border-color);
      }

      &:is(.-internet, .-hold) {
        color: var(--flagged-color);
        background-color: var(--flagged-background);
        /* Keep the border visible for flagged rows */
        border-bottom-color: var(--row-border-color);

        &.-highlighted {
          color: var(--flagged-selected-color);
          background-color: var(--flagged-selected-background);
        }
      }

      .-cell-tag {
        padding: 0 .75em;
        border-radius: 50px;
      }
    }

    .-header {
      position: sticky;
      left: 0;
      top: 0;
      width: 100%;
      font-weight: bold;
      background: var(--header-background);
      backdrop-filter: var(--blur);
      z-index: 2;
      /* Use a slightly darker border for the header row */
      border-bottom: 1px solid var(--body-border);
      /* Add a subtle shadow effect */
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .-sort {
        color: color-mix(in oklch, var(--pure-black), transparent 40%);
        margin-right: .5em;
      }

      .-resizer {
        position: absolute;
        top: 0;
        right: 0;
        width: 5px;
        height: 100%;
        line-height: 100%;
        border: 2px dotted var(--pure-aqua);
        border-top: none;
        border-bottom: none;
        cursor: ew-resize;
        opacity: 0;
        transition: all .2s .4s;
      }

      &:hover {
        .-resizer {
          opacity: 1;
        }
      }
    }
  }

  > .loadport {
    display: flex;
    justify-content: center;
    align-items: center;

    ._indicator {
      width: 15rem;
    }
  }
}
